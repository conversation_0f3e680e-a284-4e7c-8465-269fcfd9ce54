#!/bin/bash

# GuoBiaoDietitian Android App Build Script
# 国标营养师Android应用构建脚本

echo "🚀 开始构建 GuoBiaoDietitian Android 应用..."
echo "📱 Building GuoBiaoDietitian Android App..."

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "❌ Java 未安装或未在PATH中找到"
    echo "❌ Java not found. Please install Java 17 or later."
    exit 1
fi

# 检查Android SDK
if [ -z "$ANDROID_HOME" ]; then
    echo "⚠️  ANDROID_HOME 环境变量未设置"
    echo "⚠️  ANDROID_HOME environment variable not set"
    echo "📝 请设置 ANDROID_HOME 指向您的 Android SDK 路径"
    echo "📝 Please set ANDROID_HOME to your Android SDK path"
fi

# 赋予gradlew执行权限
chmod +x gradlew

echo "🔧 清理项目..."
echo "🔧 Cleaning project..."
./gradlew clean

echo "🔨 编译项目..."
echo "🔨 Building project..."
./gradlew assembleDebug

if [ $? -eq 0 ]; then
    echo "✅ 构建成功！"
    echo "✅ Build successful!"
    echo "📦 APK 文件位置: app/build/outputs/apk/debug/"
    echo "📦 APK location: app/build/outputs/apk/debug/"
    
    # 显示APK信息
    if [ -f "app/build/outputs/apk/debug/app-debug.apk" ]; then
        APK_SIZE=$(du -h app/build/outputs/apk/debug/app-debug.apk | cut -f1)
        echo "📊 APK 大小: $APK_SIZE"
        echo "📊 APK size: $APK_SIZE"
    fi
else
    echo "❌ 构建失败"
    echo "❌ Build failed"
    exit 1
fi

echo ""
echo "🎉 构建完成！"
echo "🎉 Build completed!"
echo ""
echo "📋 下一步："
echo "📋 Next steps:"
echo "   1. 连接Android设备或启动模拟器"
echo "   1. Connect Android device or start emulator"
echo "   2. 运行: ./gradlew installDebug"
echo "   2. Run: ./gradlew installDebug"
echo "   3. 或手动安装APK文件"
echo "   3. Or manually install the APK file"
