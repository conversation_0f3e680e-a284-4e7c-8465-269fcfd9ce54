<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.GuoBiaoDietitian" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Customize your light theme here. -->
        <item name="colorPrimary">@color/green_40</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/green_grey_40</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="colorTertiary">@color/orange_40</item>
        <item name="colorOnTertiary">@color/white</item>
        <item name="android:statusBarColor">?attr/colorPrimary</item>
    </style>

    <style name="Theme.GuoBiaoDietitian" parent="Base.Theme.GuoBiaoDietitian" />
</resources>
