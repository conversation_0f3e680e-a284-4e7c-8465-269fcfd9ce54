package com.borealbit.gbdietitian.android.data.local.database

import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.borealbit.gbdietitian.android.data.local.dao.FoodDao
import com.borealbit.gbdietitian.android.data.local.dao.NutritionLogDao
import com.borealbit.gbdietitian.android.data.local.dao.UserProfileDao
import com.borealbit.gbdietitian.android.data.local.entity.FoodEntity
import com.borealbit.gbdietitian.android.data.local.entity.NutritionLogEntity
import com.borealbit.gbdietitian.android.data.local.entity.UserProfileEntity

@Database(
    entities = [
        FoodEntity::class,
        NutritionLogEntity::class,
        UserProfileEntity::class
    ],
    version = 1,
    exportSchema = true
)
@TypeConverters(DatabaseConverters::class)
abstract class GuoBiaoDietitianDatabase : RoomDatabase() {

    abstract fun foodDao(): FoodDao
    abstract fun nutritionLogDao(): NutritionLogDao
    abstract fun userProfileDao(): UserProfileDao

    companion object {
        const val DATABASE_NAME = "guobiao_dietitian_database"
    }
}
