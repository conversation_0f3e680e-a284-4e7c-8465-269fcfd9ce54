package com.borealbit.gbdietitian.android.presentation.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Restaurant
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import com.borealbit.gbdietitian.android.domain.model.Food
import com.borealbit.gbdietitian.android.domain.model.FoodSource
import com.borealbit.gbdietitian.android.domain.model.NutritionInfo
import com.borealbit.gbdietitian.android.presentation.theme.GuoBiaoDietitianTheme

@Composable
fun FoodItem(
    food: Food,
    onFoodClick: (Food) -> Unit,
    modifier: Modifier = Modifier,
    showNutrition: Boolean = true
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onFoodClick(food) },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 食物图片或图标
            Box(
                modifier = Modifier
                    .size(60.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(MaterialTheme.colorScheme.surfaceVariant),
                contentAlignment = Alignment.Center
            ) {
                if (food.imageUrl != null) {
                    AsyncImage(
                        model = food.imageUrl,
                        contentDescription = food.name,
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.Crop
                    )
                } else {
                    Icon(
                        imageVector = Icons.Default.Restaurant,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.size(24.dp)
                    )
                }
            }

            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = food.name,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.weight(1f)
                    )

                    if (food.isGbCertified) {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = "国标认证",
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }

                if (food.brand != null) {
                    Text(
                        text = food.brand,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }

                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Chip(
                        text = food.category,
                        color = MaterialTheme.colorScheme.secondaryContainer
                    )

                    if (food.source == FoodSource.AI_RECOGNITION && food.confidence != null) {
                        Chip(
                            text = "AI ${(food.confidence * 100).toInt()}%",
                            color = MaterialTheme.colorScheme.tertiaryContainer
                        )
                    }
                }

                if (showNutrition) {
                    Text(
                        text = "${food.nutrition.calories.toInt()} kcal/100g",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }
    }
}

@Composable
private fun Chip(
    text: String,
    color: Color,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .background(
                color = color,
                shape = CircleShape
            )
            .padding(horizontal = 8.dp, vertical = 4.dp)
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.labelSmall,
            color = MaterialTheme.colorScheme.onSecondaryContainer
        )
    }
}

@Preview(showBackground = true)
@Composable
fun FoodItemPreview() {
    GuoBiaoDietitianTheme {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            FoodItem(
                food = Food(
                    id = "1",
                    name = "苹果",
                    category = "水果",
                    brand = "新疆阿克苏",
                    nutrition = NutritionInfo(
                        calories = 52.0,
                        protein = 0.3,
                        carbohydrates = 14.0,
                        fat = 0.2
                    ),
                    source = FoodSource.AI_RECOGNITION,
                    confidence = 0.95f,
                    isGbCertified = true
                ),
                onFoodClick = {}
            )
        }
    }
}
