package com.borealbit.gbdietitian.android.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "foods")
data class FoodEntity(
    @PrimaryKey
    val id: String,
    val name: String,
    val nameEn: String? = null,
    val category: String,
    val brand: String? = null,
    val barcode: String? = null,
    
    // 营养成分 (每100g)
    val calories: Double, // 卡路里 (kcal)
    val protein: Double, // 蛋白质 (g)
    val carbohydrates: Double, // 碳水化合物 (g)
    val fat: Double, // 脂肪 (g)
    val fiber: Double? = null, // 膳食纤维 (g)
    val sugar: Double? = null, // 糖 (g)
    val sodium: Double? = null, // 钠 (mg)
    val potassium: Double? = null, // 钾 (mg)
    val calcium: Double? = null, // 钙 (mg)
    val iron: Double? = null, // 铁 (mg)
    val vitaminC: Double? = null, // 维生素C (mg)
    val vitaminA: Double? = null, // 维生素A (μg)
    
    // 国标相关
    val gbStandard: String? = null, // 对应的国标编号
    val isGbCertified: Boolean = false, // 是否通过国标认证
    
    // 元数据
    val source: String, // 数据来源: "manual", "scan", "api", "ai_recognition"
    val confidence: Float? = null, // AI识别置信度 (0-1)
    val imageUrl: String? = null, // 食物图片URL
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)
