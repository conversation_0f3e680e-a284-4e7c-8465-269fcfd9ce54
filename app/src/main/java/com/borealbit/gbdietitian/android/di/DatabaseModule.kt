package com.borealbit.gbdietitian.android.di

import android.content.Context
import androidx.room.Room
import com.borealbit.gbdietitian.android.data.local.database.GuoBiaoDietitianDatabase
import com.borealbit.gbdietitian.android.data.local.dao.FoodDao
import com.borealbit.gbdietitian.android.data.local.dao.NutritionLogDao
import com.borealbit.gbdietitian.android.data.local.dao.UserProfileDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {

    @Provides
    @Singleton
    fun provideGuoBiaoDietitianDatabase(
        @ApplicationContext context: Context
    ): GuoBiaoDietitianDatabase {
        return Room.databaseBuilder(
            context,
            GuoBiaoDietitianDatabase::class.java,
            "guobiao_dietitian_database"
        )
            .fallbackToDestructiveMigration() // 开发阶段使用，生产环境需要提供迁移策略
            .build()
    }

    @Provides
    fun provideFoodDao(database: GuoBiaoDietitianDatabase): FoodDao {
        return database.foodDao()
    }

    @Provides
    fun provideNutritionLogDao(database: GuoBiaoDietitianDatabase): NutritionLogDao {
        return database.nutritionLogDao()
    }

    @Provides
    fun provideUserProfileDao(database: GuoBiaoDietitianDatabase): UserProfileDao {
        return database.userProfileDao()
    }
}
