package com.borealbit.gbdietitian.android.data.local.dao

import androidx.room.*
import com.borealbit.gbdietitian.android.data.local.entity.UserProfileEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface UserProfileDao {

    @Query("SELECT * FROM user_profiles WHERE id = :id")
    suspend fun getUserById(id: String): UserProfileEntity?

    @Query("SELECT * FROM user_profiles WHERE email = :email LIMIT 1")
    suspend fun getUserByEmail(email: String): UserProfileEntity?

    @Query("SELECT * FROM user_profiles ORDER BY createdAt DESC LIMIT 1")
    suspend fun getCurrentUser(): UserProfileEntity?

    @Query("SELECT * FROM user_profiles ORDER BY createdAt DESC LIMIT 1")
    fun getCurrentUserFlow(): Flow<UserProfileEntity?>

    @Query("SELECT * FROM user_profiles")
    fun getAllUsers(): Flow<List<UserProfileEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUser(user: UserProfileEntity)

    @Update
    suspend fun updateUser(user: UserProfileEntity)

    @Delete
    suspend fun deleteUser(user: UserProfileEntity)

    @Query("DELETE FROM user_profiles WHERE id = :id")
    suspend fun deleteUserById(id: String)

    @Query("SELECT COUNT(*) FROM user_profiles")
    suspend fun getUserCount(): Int

    @Query("UPDATE user_profiles SET weight = :weight, updatedAt = :timestamp WHERE id = :userId")
    suspend fun updateWeight(userId: String, weight: Double, timestamp: Long = System.currentTimeMillis())

    @Query("UPDATE user_profiles SET targetCalories = :calories, updatedAt = :timestamp WHERE id = :userId")
    suspend fun updateTargetCalories(userId: String, calories: Double, timestamp: Long = System.currentTimeMillis())

    @Query("UPDATE user_profiles SET enableHealthConnect = :enabled, updatedAt = :timestamp WHERE id = :userId")
    suspend fun updateHealthConnectStatus(userId: String, enabled: Boolean, timestamp: Long = System.currentTimeMillis())
}
