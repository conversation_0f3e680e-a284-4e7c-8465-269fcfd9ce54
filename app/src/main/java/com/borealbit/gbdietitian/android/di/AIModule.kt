package com.borealbit.gbdietitian.android.di

import com.borealbit.gbdietitian.android.ai.FoodRecognitionAI
import com.borealbit.gbdietitian.android.ai.NutritionChatAI
import com.borealbit.gbdietitian.android.ai.gemini.GeminiNanoManager
import com.borealbit.gbdietitian.android.ai.onnx.ONNXFoodRecognition
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class AIModule {

    @Binds
    @Singleton
    abstract fun bindFoodRecognitionAI(
        onnxFoodRecognition: ONNXFoodRecognition
    ): FoodRecognitionAI

    @Binds
    @Singleton
    abstract fun bindNutritionChatAI(
        geminiNanoManager: GeminiNanoManager
    ): NutritionChatAI
}
