package com.borealbit.gbdietitian.android.data.repository

import com.borealbit.gbdietitian.android.data.local.dao.UserProfileDao
import com.borealbit.gbdietitian.android.data.mapper.toDomain
import com.borealbit.gbdietitian.android.data.mapper.toEntity
import com.borealbit.gbdietitian.android.domain.model.UserProfile
import com.borealbit.gbdietitian.android.domain.repository.UserRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class UserRepositoryImpl @Inject constructor(
    private val userProfileDao: UserProfileDao
) : UserRepository {

    override suspend fun getUserById(id: String): UserProfile? {
        return userProfileDao.getUserById(id)?.toDomain()
    }

    override suspend fun getUserByEmail(email: String): UserProfile? {
        return userProfileDao.getUserByEmail(email)?.toDomain()
    }

    override suspend fun getCurrentUser(): UserProfile? {
        return userProfileDao.getCurrentUser()?.toDomain()
    }

    override fun getCurrentUserFlow(): Flow<UserProfile?> {
        return userProfileDao.getCurrentUserFlow().map { entity ->
            entity?.toDomain()
        }
    }

    override fun getAllUsers(): Flow<List<UserProfile>> {
        return userProfileDao.getAllUsers().map { entities ->
            entities.map { it.toDomain() }
        }
    }

    override suspend fun saveUser(user: UserProfile) {
        userProfileDao.insertUser(user.toEntity())
    }

    override suspend fun updateUser(user: UserProfile) {
        userProfileDao.updateUser(user.toEntity())
    }

    override suspend fun deleteUser(user: UserProfile) {
        userProfileDao.deleteUser(user.toEntity())
    }

    override suspend fun deleteUserById(id: String) {
        userProfileDao.deleteUserById(id)
    }

    override suspend fun getUserCount(): Int {
        return userProfileDao.getUserCount()
    }

    override suspend fun updateWeight(userId: String, weight: Double) {
        userProfileDao.updateWeight(userId, weight)
    }

    override suspend fun updateTargetCalories(userId: String, calories: Double) {
        userProfileDao.updateTargetCalories(userId, calories)
    }

    override suspend fun updateHealthConnectStatus(userId: String, enabled: Boolean) {
        userProfileDao.updateHealthConnectStatus(userId, enabled)
    }

    override suspend fun updateUserPreferences(userId: String, preferences: Map<String, Any>) {
        // 获取当前用户信息
        val currentUser = getUserById(userId) ?: return
        
        // 更新偏好设置
        val updatedUser = currentUser.copy(
            preferences = currentUser.preferences.copy(
                language = preferences["language"] as? String ?: currentUser.preferences.language,
                enableNotifications = preferences["enableNotifications"] as? Boolean ?: currentUser.preferences.enableNotifications,
                enableHealthConnect = preferences["enableHealthConnect"] as? Boolean ?: currentUser.preferences.enableHealthConnect
            ),
            updatedAt = System.currentTimeMillis()
        )
        
        updateUser(updatedUser)
    }
}
