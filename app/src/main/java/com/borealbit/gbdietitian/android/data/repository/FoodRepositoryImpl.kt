package com.borealbit.gbdietitian.android.data.repository

import com.borealbit.gbdietitian.android.data.local.dao.FoodDao
import com.borealbit.gbdietitian.android.data.mapper.toDomain
import com.borealbit.gbdietitian.android.data.mapper.toEntity
import com.borealbit.gbdietitian.android.domain.model.Food
import com.borealbit.gbdietitian.android.domain.model.FoodCategory
import com.borealbit.gbdietitian.android.domain.repository.FoodRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FoodRepositoryImpl @Inject constructor(
    private val foodDao: FoodDao
) : FoodRepository {

    override suspend fun getFoodById(id: String): Food? {
        return foodDao.getFoodById(id)?.toDomain()
    }

    override suspend fun getFoodByBarcode(barcode: String): Food? {
        return foodDao.getFoodByBarcode(barcode)?.toDomain()
    }

    override fun searchFoods(query: String): Flow<List<Food>> {
        return foodDao.searchFoods(query).map { entities ->
            entities.map { it.toDomain() }
        }
    }

    override fun getFoodsByCategory(category: FoodCategory): Flow<List<Food>> {
        val categoryString = when (category) {
            FoodCategory.GRAINS -> "谷物"
            FoodCategory.VEGETABLES -> "蔬菜"
            FoodCategory.FRUITS -> "水果"
            FoodCategory.MEAT -> "肉类"
            FoodCategory.SEAFOOD -> "海鲜"
            FoodCategory.DAIRY -> "乳制品"
            FoodCategory.NUTS -> "坚果"
            FoodCategory.BEVERAGES -> "饮料"
            FoodCategory.SNACKS -> "零食"
            FoodCategory.CONDIMENTS -> "调料"
            FoodCategory.OTHER -> "其他"
        }
        return foodDao.getFoodsByCategory(categoryString).map { entities ->
            entities.map { it.toDomain() }
        }
    }

    override fun getAllCategories(): Flow<List<String>> {
        return foodDao.getAllCategories()
    }

    override fun getGbCertifiedFoods(): Flow<List<Food>> {
        return foodDao.getGbCertifiedFoods().map { entities ->
            entities.map { it.toDomain() }
        }
    }

    override fun getRecentFoods(limit: Int): Flow<List<Food>> {
        return foodDao.getRecentFoods(limit).map { entities ->
            entities.map { it.toDomain() }
        }
    }

    override suspend fun saveFood(food: Food) {
        foodDao.insertFood(food.toEntity())
    }

    override suspend fun saveFoods(foods: List<Food>) {
        foodDao.insertFoods(foods.map { it.toEntity() })
    }

    override suspend fun updateFood(food: Food) {
        foodDao.updateFood(food.toEntity())
    }

    override suspend fun deleteFood(food: Food) {
        foodDao.deleteFood(food.toEntity())
    }

    override suspend fun deleteFoodById(id: String) {
        foodDao.deleteFoodById(id)
    }

    override suspend fun getFoodCount(): Int {
        return foodDao.getFoodCount()
    }

    override suspend fun recognizeFoodFromImage(imageUri: String): List<Food> {
        // TODO: 实现AI图像识别
        // 这里应该调用AI模型进行食物识别
        return emptyList()
    }

    override suspend fun searchFoodByDescription(description: String): List<Food> {
        // TODO: 实现基于描述的食物搜索
        // 可以结合AI模型和本地数据库搜索
        return emptyList()
    }
}
