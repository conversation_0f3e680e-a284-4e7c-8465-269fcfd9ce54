package com.borealbit.gbdietitian.android.data.local.dao

import androidx.room.*
import com.borealbit.gbdietitian.android.data.local.entity.NutritionLogEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface NutritionLogDao {

    @Query("SELECT * FROM nutrition_logs WHERE id = :id")
    suspend fun getLogById(id: String): NutritionLogEntity?

    @Query("""
        SELECT * FROM nutrition_logs 
        WHERE consumedAt >= :startTime AND consumedAt <= :endTime 
        ORDER BY consumedAt DESC
    """)
    fun getLogsByDateRange(startTime: Long, endTime: Long): Flow<List<NutritionLogEntity>>

    @Query("""
        SELECT * FROM nutrition_logs 
        WHERE DATE(consumedAt / 1000, 'unixepoch') = DATE(:timestamp / 1000, 'unixepoch')
        ORDER BY consumedAt DESC
    """)
    fun getLogsByDate(timestamp: Long): Flow<List<NutritionLogEntity>>

    @Query("""
        SELECT * FROM nutrition_logs 
        WHERE mealType = :mealType 
        AND DATE(consumedAt / 1000, 'unixepoch') = DATE(:timestamp / 1000, 'unixepoch')
        ORDER BY consumedAt DESC
    """)
    fun getLogsByMealType(mealType: String, timestamp: Long): Flow<List<NutritionLogEntity>>

    @Query("""
        SELECT SUM(actualCalories) as totalCalories,
               SUM(actualProtein) as totalProtein,
               SUM(actualCarbohydrates) as totalCarbs,
               SUM(actualFat) as totalFat,
               SUM(actualFiber) as totalFiber,
               SUM(actualSugar) as totalSugar,
               SUM(actualSodium) as totalSodium
        FROM nutrition_logs 
        WHERE DATE(consumedAt / 1000, 'unixepoch') = DATE(:timestamp / 1000, 'unixepoch')
    """)
    suspend fun getDailyNutritionSummary(timestamp: Long): DailyNutritionSummary?

    @Query("SELECT * FROM nutrition_logs WHERE userId = :userId ORDER BY consumedAt DESC")
    fun getLogsByUser(userId: String): Flow<List<NutritionLogEntity>>

    @Query("SELECT * FROM nutrition_logs ORDER BY consumedAt DESC LIMIT :limit")
    fun getRecentLogs(limit: Int = 50): Flow<List<NutritionLogEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertLog(log: NutritionLogEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertLogs(logs: List<NutritionLogEntity>)

    @Update
    suspend fun updateLog(log: NutritionLogEntity)

    @Delete
    suspend fun deleteLog(log: NutritionLogEntity)

    @Query("DELETE FROM nutrition_logs WHERE id = :id")
    suspend fun deleteLogById(id: String)

    @Query("SELECT COUNT(*) FROM nutrition_logs")
    suspend fun getLogCount(): Int
}

data class DailyNutritionSummary(
    val totalCalories: Double?,
    val totalProtein: Double?,
    val totalCarbs: Double?,
    val totalFat: Double?,
    val totalFiber: Double?,
    val totalSugar: Double?,
    val totalSodium: Double?
)
