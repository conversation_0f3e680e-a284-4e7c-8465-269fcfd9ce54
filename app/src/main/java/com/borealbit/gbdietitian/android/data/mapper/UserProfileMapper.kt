package com.borealbit.gbdietitian.android.data.mapper

import com.borealbit.gbdietitian.android.data.local.entity.UserProfileEntity
import com.borealbit.gbdietitian.android.domain.model.*
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString

fun UserProfileEntity.toDomain(): UserProfile {
    return UserProfile(
        id = id,
        name = name,
        email = email,
        age = age,
        gender = when (gender) {
            "male" -> Gender.MALE
            "female" -> Gender.FEMALE
            "other" -> Gender.OTHER
            else -> Gender.OTHER
        },
        height = height,
        weight = weight,
        activityLevel = when (activityLevel) {
            "sedentary" -> ActivityLevel.SEDENTARY
            "light" -> ActivityLevel.LIGHT
            "moderate" -> ActivityLevel.MODERATE
            "active" -> ActivityLevel.ACTIVE
            "very_active" -> ActivityLevel.VERY_ACTIVE
            else -> ActivityLevel.MODERATE
        },
        healthGoal = when (healthGoal) {
            "maintain" -> HealthGoal.MAINTAIN
            "lose_weight" -> HealthGoal.LOSE_WEIGHT
            "gain_weight" -> HealthGoal.GAIN_WEIGHT
            "muscle_gain" -> HealthGoal.MUSCLE_GAIN
            else -> HealthGoal.MAINTAIN
        },
        targetWeight = targetWeight,
        targetCalories = targetCalories,
        useGbStandards = useGbStandards,
        customMacroRatios = if (customProteinRatio != null && customCarbRatio != null && customFatRatio != null) {
            MacroRatios(
                proteinRatio = customProteinRatio,
                carbRatio = customCarbRatio,
                fatRatio = customFatRatio
            )
        } else null,
        healthConditions = HealthConditions(
            hasHypertension = hasHypertension,
            hasDiabetes = hasDiabetes,
            hasHeartDisease = hasHeartDisease
        ),
        allergies = allergies?.let { 
            try { Json.decodeFromString<List<String>>(it) } catch (e: Exception) { emptyList() }
        } ?: emptyList(),
        medications = medications?.let { 
            try { Json.decodeFromString<List<String>>(it) } catch (e: Exception) { emptyList() }
        } ?: emptyList(),
        preferences = UserPreferences(
            preferredUnits = when (preferredUnits) {
                "metric" -> Units.METRIC
                "imperial" -> Units.IMPERIAL
                else -> Units.METRIC
            },
            language = language,
            enableNotifications = enableNotifications,
            enableHealthConnect = enableHealthConnect
        ),
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}

fun UserProfile.toEntity(): UserProfileEntity {
    return UserProfileEntity(
        id = id,
        name = name,
        email = email,
        age = age,
        gender = when (gender) {
            Gender.MALE -> "male"
            Gender.FEMALE -> "female"
            Gender.OTHER -> "other"
        },
        height = height,
        weight = weight,
        activityLevel = when (activityLevel) {
            ActivityLevel.SEDENTARY -> "sedentary"
            ActivityLevel.LIGHT -> "light"
            ActivityLevel.MODERATE -> "moderate"
            ActivityLevel.ACTIVE -> "active"
            ActivityLevel.VERY_ACTIVE -> "very_active"
        },
        healthGoal = when (healthGoal) {
            HealthGoal.MAINTAIN -> "maintain"
            HealthGoal.LOSE_WEIGHT -> "lose_weight"
            HealthGoal.GAIN_WEIGHT -> "gain_weight"
            HealthGoal.MUSCLE_GAIN -> "muscle_gain"
        },
        targetWeight = targetWeight,
        targetCalories = targetCalories,
        useGbStandards = useGbStandards,
        customProteinRatio = customMacroRatios?.proteinRatio,
        customCarbRatio = customMacroRatios?.carbRatio,
        customFatRatio = customMacroRatios?.fatRatio,
        hasHypertension = healthConditions.hasHypertension,
        hasDiabetes = healthConditions.hasDiabetes,
        hasHeartDisease = healthConditions.hasHeartDisease,
        allergies = if (allergies.isNotEmpty()) Json.encodeToString(allergies) else null,
        medications = if (medications.isNotEmpty()) Json.encodeToString(medications) else null,
        preferredUnits = when (preferences.preferredUnits) {
            Units.METRIC -> "metric"
            Units.IMPERIAL -> "imperial"
        },
        language = preferences.language,
        enableNotifications = preferences.enableNotifications,
        enableHealthConnect = preferences.enableHealthConnect,
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}
