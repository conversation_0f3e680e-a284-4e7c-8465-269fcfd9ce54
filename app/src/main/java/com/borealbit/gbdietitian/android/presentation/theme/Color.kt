package com.borealbit.gbdietitian.android.presentation.theme

import androidx.compose.ui.graphics.Color

// Primary colors - 健康绿色主题
val Green80 = Color(0xFF4CAF50)
val GreenGrey80 = Color(0xFF81C784)
val Orange80 = Color(0xFFFF9800)

val Green40 = Color(0xFF2E7D32)
val GreenGrey40 = Color(0xFF388E3C)
val Orange40 = Color(0xFFE65100)

// Nutrition status colors
val NutrientGood = Color(0xFF4CAF50)      // 绿色 - 营养良好
val NutrientWarning = Color(0xFFFF9800)   // 橙色 - 需要注意
val NutrientDanger = Color(0xFFF44336)    // 红色 - 超标警告

// Background colors
val LightBackground = Color(0xFFFAFAFA)
val DarkBackground = Color(0xFF121212)
