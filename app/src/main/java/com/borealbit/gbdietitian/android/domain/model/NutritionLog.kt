package com.borealbit.gbdietitian.android.domain.model

data class NutritionLog(
    val id: String,
    val foodId: String,
    val userId: String? = null,
    
    // 摄入信息
    val amount: Double, // 摄入量 (g)
    val mealType: MealType,
    val consumedAt: Long,
    
    // 计算后的营养值
    val actualNutrition: NutritionInfo,
    
    // 记录方式
    val logMethod: LogMethod,
    val notes: String? = null,
    val imageUrl: String? = null,
    
    // 元数据
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

enum class MealType {
    BREAKFAST,
    LUNCH,
    DINNER,
    SNACK
}

enum class LogMethod {
    PHOTO,
    BARCODE,
    MANUAL,
    VOICE
}

data class DailyNutritionSummary(
    val date: Long,
    val totalCalories: Double,
    val totalProtein: Double,
    val totalCarbohydrates: Double,
    val totalFat: Double,
    val totalFiber: Double,
    val totalSugar: Double,
    val totalSodium: Double,
    val mealBreakdown: Map<MealType, NutritionInfo>
)
