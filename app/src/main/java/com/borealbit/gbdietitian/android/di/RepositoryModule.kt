package com.borealbit.gbdietitian.android.di

import com.borealbit.gbdietitian.android.data.repository.FoodRepositoryImpl
import com.borealbit.gbdietitian.android.data.repository.NutritionRepositoryImpl
import com.borealbit.gbdietitian.android.data.repository.UserRepositoryImpl
import com.borealbit.gbdietitian.android.domain.repository.FoodRepository
import com.borealbit.gbdietitian.android.domain.repository.NutritionRepository
import com.borealbit.gbdietitian.android.domain.repository.UserRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {

    @Binds
    @Singleton
    abstract fun bindFoodRepository(
        foodRepositoryImpl: FoodRepositoryImpl
    ): FoodRepository

    @Binds
    @Singleton
    abstract fun bindNutritionRepository(
        nutritionRepositoryImpl: NutritionRepositoryImpl
    ): NutritionRepository

    @Binds
    @Singleton
    abstract fun bindUserRepository(
        userRepositoryImpl: UserRepositoryImpl
    ): UserRepository
}
