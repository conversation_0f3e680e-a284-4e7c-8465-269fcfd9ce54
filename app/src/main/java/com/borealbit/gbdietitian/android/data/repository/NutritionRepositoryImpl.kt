package com.borealbit.gbdietitian.android.data.repository

import com.borealbit.gbdietitian.android.data.local.dao.NutritionLogDao
import com.borealbit.gbdietitian.android.data.mapper.toDomain
import com.borealbit.gbdietitian.android.data.mapper.toEntity
import com.borealbit.gbdietitian.android.domain.model.DailyNutritionSummary
import com.borealbit.gbdietitian.android.domain.model.MealType
import com.borealbit.gbdietitian.android.domain.model.NutritionInfo
import com.borealbit.gbdietitian.android.domain.model.NutritionLog
import com.borealbit.gbdietitian.android.domain.repository.NutritionRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class NutritionRepositoryImpl @Inject constructor(
    private val nutritionLogDao: NutritionLogDao
) : NutritionRepository {

    override suspend fun getLogById(id: String): NutritionLog? {
        return nutritionLogDao.getLogById(id)?.toDomain()
    }

    override fun getLogsByDateRange(startTime: Long, endTime: Long): Flow<List<NutritionLog>> {
        return nutritionLogDao.getLogsByDateRange(startTime, endTime).map { entities ->
            entities.map { it.toDomain() }
        }
    }

    override fun getLogsByDate(timestamp: Long): Flow<List<NutritionLog>> {
        return nutritionLogDao.getLogsByDate(timestamp).map { entities ->
            entities.map { it.toDomain() }
        }
    }

    override fun getLogsByMealType(mealType: MealType, timestamp: Long): Flow<List<NutritionLog>> {
        val mealTypeString = when (mealType) {
            MealType.BREAKFAST -> "breakfast"
            MealType.LUNCH -> "lunch"
            MealType.DINNER -> "dinner"
            MealType.SNACK -> "snack"
        }
        return nutritionLogDao.getLogsByMealType(mealTypeString, timestamp).map { entities ->
            entities.map { it.toDomain() }
        }
    }

    override suspend fun getDailyNutritionSummary(timestamp: Long): DailyNutritionSummary? {
        val summary = nutritionLogDao.getDailyNutritionSummary(timestamp)
        return summary?.let {
            DailyNutritionSummary(
                date = timestamp,
                totalCalories = it.totalCalories ?: 0.0,
                totalProtein = it.totalProtein ?: 0.0,
                totalCarbohydrates = it.totalCarbs ?: 0.0,
                totalFat = it.totalFat ?: 0.0,
                totalFiber = it.totalFiber ?: 0.0,
                totalSugar = it.totalSugar ?: 0.0,
                totalSodium = it.totalSodium ?: 0.0,
                mealBreakdown = emptyMap() // TODO: 实现餐次分解
            )
        }
    }

    override fun getRecentLogs(limit: Int): Flow<List<NutritionLog>> {
        return nutritionLogDao.getRecentLogs(limit).map { entities ->
            entities.map { it.toDomain() }
        }
    }

    override suspend fun saveLog(log: NutritionLog) {
        nutritionLogDao.insertLog(log.toEntity())
    }

    override suspend fun saveLogs(logs: List<NutritionLog>) {
        nutritionLogDao.insertLogs(logs.map { it.toEntity() })
    }

    override suspend fun updateLog(log: NutritionLog) {
        nutritionLogDao.updateLog(log.toEntity())
    }

    override suspend fun deleteLog(log: NutritionLog) {
        nutritionLogDao.deleteLog(log.toEntity())
    }

    override suspend fun deleteLogById(id: String) {
        nutritionLogDao.deleteLogById(id)
    }

    override suspend fun getLogCount(): Int {
        return nutritionLogDao.getLogCount()
    }

    override suspend fun analyzeNutritionTrends(days: Int): List<DailyNutritionSummary> {
        val endTime = System.currentTimeMillis()
        val startTime = endTime - (days * 24 * 60 * 60 * 1000L)
        
        val summaries = mutableListOf<DailyNutritionSummary>()
        val calendar = Calendar.getInstance()
        
        for (i in 0 until days) {
            calendar.timeInMillis = endTime - (i * 24 * 60 * 60 * 1000L)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            
            val dayTimestamp = calendar.timeInMillis
            getDailyNutritionSummary(dayTimestamp)?.let { summary ->
                summaries.add(summary)
            }
        }
        
        return summaries.reversed() // 返回按时间正序排列的数据
    }

    override suspend fun getWeeklyNutritionSummary(): List<DailyNutritionSummary> {
        return analyzeNutritionTrends(7)
    }

    override suspend fun getMonthlyNutritionSummary(): List<DailyNutritionSummary> {
        return analyzeNutritionTrends(30)
    }
}
