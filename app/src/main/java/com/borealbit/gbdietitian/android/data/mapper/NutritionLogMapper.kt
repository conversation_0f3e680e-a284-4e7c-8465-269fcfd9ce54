package com.borealbit.gbdietitian.android.data.mapper

import com.borealbit.gbdietitian.android.data.local.entity.NutritionLogEntity
import com.borealbit.gbdietitian.android.domain.model.LogMethod
import com.borealbit.gbdietitian.android.domain.model.MealType
import com.borealbit.gbdietitian.android.domain.model.NutritionInfo
import com.borealbit.gbdietitian.android.domain.model.NutritionLog

fun NutritionLogEntity.toDomain(): NutritionLog {
    return NutritionLog(
        id = id,
        foodId = foodId,
        userId = userId,
        amount = amount,
        mealType = when (mealType) {
            "breakfast" -> MealType.BREAKFAST
            "lunch" -> MealType.LUNCH
            "dinner" -> MealType.DINNER
            "snack" -> MealType.SNACK
            else -> MealType.SNACK
        },
        consumedAt = consumedAt,
        actualNutrition = NutritionInfo(
            calories = actualCalories,
            protein = actualProtein,
            carbohydrates = actualCarbohydrates,
            fat = actualFat,
            fiber = actualFiber,
            sugar = actualSugar,
            sodium = actualSodium
        ),
        logMethod = when (logMethod) {
            "photo" -> LogMethod.PHOTO
            "barcode" -> LogMethod.BARCODE
            "manual" -> LogMethod.MANUAL
            "voice" -> LogMethod.VOICE
            else -> LogMethod.MANUAL
        },
        notes = notes,
        imageUrl = imageUrl,
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}

fun NutritionLog.toEntity(): NutritionLogEntity {
    return NutritionLogEntity(
        id = id,
        foodId = foodId,
        userId = userId,
        amount = amount,
        mealType = when (mealType) {
            MealType.BREAKFAST -> "breakfast"
            MealType.LUNCH -> "lunch"
            MealType.DINNER -> "dinner"
            MealType.SNACK -> "snack"
        },
        consumedAt = consumedAt,
        actualCalories = actualNutrition.calories,
        actualProtein = actualNutrition.protein,
        actualCarbohydrates = actualNutrition.carbohydrates,
        actualFat = actualNutrition.fat,
        actualFiber = actualNutrition.fiber,
        actualSugar = actualNutrition.sugar,
        actualSodium = actualNutrition.sodium,
        logMethod = when (logMethod) {
            LogMethod.PHOTO -> "photo"
            LogMethod.BARCODE -> "barcode"
            LogMethod.MANUAL -> "manual"
            LogMethod.VOICE -> "voice"
        },
        notes = notes,
        imageUrl = imageUrl,
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}
