package com.borealbit.gbdietitian.android.presentation.components

import android.Manifest
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Camera
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.google.accompanist.permissions.*
import com.borealbit.gbdietitian.android.presentation.theme.GuoBiaoDietitianTheme

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun CameraPermissionHandler(
    onPermissionGranted: () -> Unit,
    onPermissionDenied: () -> Unit = {},
    content: @Composable () -> Unit
) {
    val cameraPermissionState = rememberPermissionState(
        permission = Manifest.permission.CAMERA
    )

    LaunchedEffect(cameraPermissionState.status) {
        when (cameraPermissionState.status) {
            is PermissionStatus.Granted -> {
                onPermissionGranted()
            }
            is PermissionStatus.Denied -> {
                onPermissionDenied()
            }
        }
    }

    when (cameraPermissionState.status) {
        is PermissionStatus.Granted -> {
            content()
        }
        is PermissionStatus.Denied -> {
            CameraPermissionDeniedContent(
                shouldShowRationale = cameraPermissionState.status.shouldShowRationale,
                onRequestPermission = { cameraPermissionState.launchPermissionRequest() }
            )
        }
    }
}

@Composable
private fun CameraPermissionDeniedContent(
    shouldShowRationale: Boolean,
    onRequestPermission: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.Camera,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.primary
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "需要相机权限",
            style = MaterialTheme.typography.headlineSmall,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = if (shouldShowRationale) {
                "为了识别食物，我们需要使用您的相机。请授予相机权限以继续使用此功能。"
            } else {
                "相机权限被拒绝。请在设置中手动开启相机权限。"
            },
            style = MaterialTheme.typography.bodyMedium,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(24.dp))

        if (shouldShowRationale) {
            Button(
                onClick = onRequestPermission,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("授予权限")
            }
        } else {
            OutlinedButton(
                onClick = { /* 打开应用设置 */ },
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    imageVector = Icons.Default.Settings,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("打开设置")
            }
        }
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun HealthConnectPermissionHandler(
    permissions: List<String>,
    onAllPermissionsGranted: () -> Unit,
    onPermissionsDenied: () -> Unit = {},
    content: @Composable () -> Unit
) {
    val permissionsState = rememberMultiplePermissionsState(permissions)

    LaunchedEffect(permissionsState.allPermissionsGranted) {
        if (permissionsState.allPermissionsGranted) {
            onAllPermissionsGranted()
        } else {
            onPermissionsDenied()
        }
    }

    when {
        permissionsState.allPermissionsGranted -> {
            content()
        }
        else -> {
            HealthPermissionDeniedContent(
                permissionsState = permissionsState
            )
        }
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
private fun HealthPermissionDeniedContent(
    permissionsState: MultiplePermissionsState
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "🏥",
            style = MaterialTheme.typography.displayMedium
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "健康数据权限",
            style = MaterialTheme.typography.headlineSmall,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = "为了提供个性化的营养建议，我们需要访问您的健康数据（如体重、步数等）。",
            style = MaterialTheme.typography.bodyMedium,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(24.dp))

        Button(
            onClick = { permissionsState.launchMultiplePermissionRequest() },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("授予权限")
        }

        Spacer(modifier = Modifier.height(8.dp))

        TextButton(
            onClick = { /* 跳过权限 */ },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("暂时跳过")
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PermissionHandlerPreview() {
    GuoBiaoDietitianTheme {
        CameraPermissionDeniedContent(
            shouldShowRationale = true,
            onRequestPermission = {}
        )
    }
}
