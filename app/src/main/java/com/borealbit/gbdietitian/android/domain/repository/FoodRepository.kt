package com.borealbit.gbdietitian.android.domain.repository

import com.borealbit.gbdietitian.android.domain.model.Food
import com.borealbit.gbdietitian.android.domain.model.FoodCategory
import kotlinx.coroutines.flow.Flow

interface FoodRepository {

    suspend fun getFoodById(id: String): Food?

    suspend fun getFoodByBarcode(barcode: String): Food?

    fun searchFoods(query: String): Flow<List<Food>>

    fun getFoodsByCategory(category: FoodCategory): Flow<List<Food>>

    fun getAllCategories(): Flow<List<String>>

    fun getGbCertifiedFoods(): Flow<List<Food>>

    fun getRecentFoods(limit: Int = 20): Flow<List<Food>>

    suspend fun saveFood(food: Food)

    suspend fun saveFoods(foods: List<Food>)

    suspend fun updateFood(food: Food)

    suspend fun deleteFood(food: Food)

    suspend fun deleteFoodById(id: String)

    suspend fun getFoodCount(): Int

    // AI相关方法
    suspend fun recognizeFoodFromImage(imageUri: String): List<Food>

    suspend fun searchFoodByDescription(description: String): List<Food>
}
