package com.borealbit.gbdietitian.android.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "user_profiles")
data class UserProfileEntity(
    @PrimaryKey
    val id: String,
    val name: String,
    val email: String? = null,
    
    // 基本信息
    val age: Int,
    val gender: String, // "male", "female", "other"
    val height: Double, // 身高 (cm)
    val weight: Double, // 体重 (kg)
    val activityLevel: String, // 活动水平: "sedentary", "light", "moderate", "active", "very_active"
    
    // 健康目标
    val healthGoal: String, // "maintain", "lose_weight", "gain_weight", "muscle_gain"
    val targetWeight: Double? = null, // 目标体重 (kg)
    val targetCalories: Double? = null, // 目标卡路里 (kcal/day)
    
    // 国标个性化设置
    val useGbStandards: Boolean = true, // 是否使用国标推荐量
    val customProteinRatio: Double? = null, // 自定义蛋白质比例 (0-1)
    val customCarbRatio: Double? = null, // 自定义碳水比例 (0-1)
    val customFatRatio: Double? = null, // 自定义脂肪比例 (0-1)
    
    // 健康状况
    val hasHypertension: Boolean = false, // 高血压
    val hasDiabetes: Boolean = false, // 糖尿病
    val hasHeartDisease: Boolean = false, // 心脏病
    val allergies: String? = null, // 过敏信息，JSON格式
    val medications: String? = null, // 用药信息，JSON格式
    
    // 偏好设置
    val preferredUnits: String = "metric", // "metric", "imperial"
    val language: String = "zh", // "zh", "en"
    val enableNotifications: Boolean = true,
    val enableHealthConnect: Boolean = false,
    
    // 元数据
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)
