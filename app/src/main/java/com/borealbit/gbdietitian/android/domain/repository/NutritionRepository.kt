package com.borealbit.gbdietitian.android.domain.repository

import com.borealbit.gbdietitian.android.domain.model.DailyNutritionSummary
import com.borealbit.gbdietitian.android.domain.model.MealType
import com.borealbit.gbdietitian.android.domain.model.NutritionLog
import kotlinx.coroutines.flow.Flow

interface NutritionRepository {

    suspend fun getLogById(id: String): NutritionLog?

    fun getLogsByDateRange(startTime: Long, endTime: Long): Flow<List<NutritionLog>>

    fun getLogsByDate(timestamp: Long): Flow<List<NutritionLog>>

    fun getLogsByMealType(mealType: MealType, timestamp: Long): Flow<List<NutritionLog>>

    suspend fun getDailyNutritionSummary(timestamp: Long): DailyNutritionSummary?

    fun getRecentLogs(limit: Int = 50): Flow<List<NutritionLog>>

    suspend fun saveLog(log: NutritionLog)

    suspend fun saveLogs(logs: List<NutritionLog>)

    suspend fun updateLog(log: NutritionLog)

    suspend fun deleteLog(log: NutritionLog)

    suspend fun deleteLogById(id: String)

    suspend fun getLogCount(): Int

    // 营养分析相关
    suspend fun analyzeNutritionTrends(days: Int = 7): List<DailyNutritionSummary>

    suspend fun getWeeklyNutritionSummary(): List<DailyNutritionSummary>

    suspend fun getMonthlyNutritionSummary(): List<DailyNutritionSummary>
}
