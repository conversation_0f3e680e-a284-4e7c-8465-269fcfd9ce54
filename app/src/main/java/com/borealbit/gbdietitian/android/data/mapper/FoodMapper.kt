package com.borealbit.gbdietitian.android.data.mapper

import com.borealbit.gbdietitian.android.data.local.entity.FoodEntity
import com.borealbit.gbdietitian.android.domain.model.Food
import com.borealbit.gbdietitian.android.domain.model.FoodSource
import com.borealbit.gbdietitian.android.domain.model.NutritionInfo

fun FoodEntity.toDomain(): Food {
    return Food(
        id = id,
        name = name,
        nameEn = nameEn,
        category = category,
        brand = brand,
        barcode = barcode,
        nutrition = NutritionInfo(
            calories = calories,
            protein = protein,
            carbohydrates = carbohydrates,
            fat = fat,
            fiber = fiber,
            sugar = sugar,
            sodium = sodium,
            potassium = potassium,
            calcium = calcium,
            iron = iron,
            vitaminC = vitaminC,
            vitaminA = vitaminA
        ),
        gbStandard = gbStandard,
        isGbCertified = isGbCertified,
        source = when (source) {
            "manual" -> FoodSource.MANUAL
            "scan" -> FoodSource.SCAN
            "api" -> FoodSource.API
            "ai_recognition" -> FoodSource.AI_RECOGNITION
            else -> FoodSource.MANUAL
        },
        confidence = confidence,
        imageUrl = imageUrl,
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}

fun Food.toEntity(): FoodEntity {
    return FoodEntity(
        id = id,
        name = name,
        nameEn = nameEn,
        category = category,
        brand = brand,
        barcode = barcode,
        calories = nutrition.calories,
        protein = nutrition.protein,
        carbohydrates = nutrition.carbohydrates,
        fat = nutrition.fat,
        fiber = nutrition.fiber,
        sugar = nutrition.sugar,
        sodium = nutrition.sodium,
        potassium = nutrition.potassium,
        calcium = nutrition.calcium,
        iron = nutrition.iron,
        vitaminC = nutrition.vitaminC,
        vitaminA = nutrition.vitaminA,
        gbStandard = gbStandard,
        isGbCertified = isGbCertified,
        source = when (source) {
            FoodSource.MANUAL -> "manual"
            FoodSource.SCAN -> "scan"
            FoodSource.API -> "api"
            FoodSource.AI_RECOGNITION -> "ai_recognition"
        },
        confidence = confidence,
        imageUrl = imageUrl,
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}
