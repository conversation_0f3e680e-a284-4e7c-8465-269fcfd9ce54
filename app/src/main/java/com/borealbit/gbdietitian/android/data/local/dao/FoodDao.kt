package com.borealbit.gbdietitian.android.data.local.dao

import androidx.room.*
import com.borealbit.gbdietitian.android.data.local.entity.FoodEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface FoodDao {

    @Query("SELECT * FROM foods WHERE id = :id")
    suspend fun getFoodById(id: String): FoodEntity?

    @Query("SELECT * FROM foods WHERE barcode = :barcode LIMIT 1")
    suspend fun getFoodByBarcode(barcode: String): FoodEntity?

    @Query("SELECT * FROM foods WHERE name LIKE '%' || :query || '%' OR nameEn LIKE '%' || :query || '%'")
    fun searchFoods(query: String): Flow<List<FoodEntity>>

    @Query("SELECT * FROM foods WHERE category = :category")
    fun getFoodsByCategory(category: String): Flow<List<FoodEntity>>

    @Query("SELECT DISTINCT category FROM foods ORDER BY category")
    fun getAllCategories(): Flow<List<String>>

    @Query("SELECT * FROM foods WHERE isGbCertified = 1")
    fun getGbCertifiedFoods(): Flow<List<FoodEntity>>

    @Query("SELECT * FROM foods ORDER BY createdAt DESC LIMIT :limit")
    fun getRecentFoods(limit: Int = 20): Flow<List<FoodEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertFood(food: FoodEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertFoods(foods: List<FoodEntity>)

    @Update
    suspend fun updateFood(food: FoodEntity)

    @Delete
    suspend fun deleteFood(food: FoodEntity)

    @Query("DELETE FROM foods WHERE id = :id")
    suspend fun deleteFoodById(id: String)

    @Query("SELECT COUNT(*) FROM foods")
    suspend fun getFoodCount(): Int

    @Query("SELECT * FROM foods WHERE source = :source")
    fun getFoodsBySource(source: String): Flow<List<FoodEntity>>
}
