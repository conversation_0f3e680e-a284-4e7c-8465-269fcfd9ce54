package com.borealbit.gbdietitian.android.domain.usecase

import android.net.Uri
import com.borealbit.gbdietitian.android.ai.FoodRecognitionAI
import com.borealbit.gbdietitian.android.ai.RecognizedFood
import com.borealbit.gbdietitian.android.domain.model.Food
import com.borealbit.gbdietitian.android.domain.model.FoodSource
import com.borealbit.gbdietitian.android.domain.repository.FoodRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.util.*
import javax.inject.Inject

/**
 * 食物识别用例
 * 处理图像识别、条码扫描等食物识别相关业务逻辑
 */
class RecognizeFoodUseCase @Inject constructor(
    private val foodRecognitionAI: FoodRecognitionAI,
    private val foodRepository: FoodRepository
) {

    /**
     * 从图像识别食物
     */
    suspend fun recognizeFromImage(imageUri: Uri): Flow<RecognitionResult> = flow {
        emit(RecognitionResult.Loading)
        
        try {
            // 使用AI模型识别食物
            val aiResult = foodRecognitionAI.recognizeFoodFromImage(imageUri)
            
            aiResult.fold(
                onSuccess = { recognizedFoods ->
                    if (recognizedFoods.isEmpty()) {
                        emit(RecognitionResult.NoFoodDetected)
                        return@fold
                    }
                    
                    // 转换为Food对象并查询详细营养信息
                    val foods = recognizedFoods.mapNotNull { recognized ->
                        convertToFood(recognized, imageUri.toString())
                    }
                    
                    emit(RecognitionResult.Success(foods))
                },
                onFailure = { error ->
                    emit(RecognitionResult.Error(error.message ?: "识别失败"))
                }
            )
        } catch (e: Exception) {
            emit(RecognitionResult.Error(e.message ?: "未知错误"))
        }
    }

    /**
     * 从条码识别食物
     */
    suspend fun recognizeFromBarcode(barcode: String): Flow<RecognitionResult> = flow {
        emit(RecognitionResult.Loading)
        
        try {
            // 首先从本地数据库查找
            val existingFood = foodRepository.getFoodByBarcode(barcode)
            if (existingFood != null) {
                emit(RecognitionResult.Success(listOf(existingFood)))
                return@flow
            }
            
            // TODO: 从网络API查询条码信息
            // val apiResult = barcodeApiService.getFoodByBarcode(barcode)
            
            // 模拟条码查询结果
            val mockFood = createMockFoodFromBarcode(barcode)
            if (mockFood != null) {
                // 保存到本地数据库
                foodRepository.saveFood(mockFood)
                emit(RecognitionResult.Success(listOf(mockFood)))
            } else {
                emit(RecognitionResult.NotFound("未找到该条码对应的食物信息"))
            }
        } catch (e: Exception) {
            emit(RecognitionResult.Error(e.message ?: "条码识别失败"))
        }
    }

    /**
     * 从文本描述识别食物
     */
    suspend fun recognizeFromText(description: String): Flow<RecognitionResult> = flow {
        emit(RecognitionResult.Loading)
        
        try {
            // 首先从本地数据库搜索
            foodRepository.searchFoods(description).collect { localFoods ->
                if (localFoods.isNotEmpty()) {
                    emit(RecognitionResult.Success(localFoods))
                    return@collect
                }
                
                // 使用AI模型进行文本识别
                val aiResult = foodRecognitionAI.recognizeFoodFromText(description)
                aiResult.fold(
                    onSuccess = { recognizedFoods ->
                        val foods = recognizedFoods.mapNotNull { recognized ->
                            convertToFood(recognized, null)
                        }
                        emit(RecognitionResult.Success(foods))
                    },
                    onFailure = { error ->
                        emit(RecognitionResult.Error(error.message ?: "文本识别失败"))
                    }
                )
            }
        } catch (e: Exception) {
            emit(RecognitionResult.Error(e.message ?: "未知错误"))
        }
    }

    private suspend fun convertToFood(recognized: RecognizedFood, imageUrl: String?): Food? {
        return try {
            // 尝试从数据库查找现有食物
            val existingFoods = foodRepository.searchFoods(recognized.name)
            existingFoods.collect { foods ->
                val existingFood = foods.firstOrNull { it.name == recognized.name }
                if (existingFood != null) {
                    return@collect existingFood.copy(
                        confidence = recognized.confidence,
                        imageUrl = imageUrl
                    )
                }
            }
            
            // 创建新的食物对象
            Food(
                id = UUID.randomUUID().toString(),
                name = recognized.name,
                category = recognized.category,
                nutrition = recognized.estimatedNutrition ?: getDefaultNutrition(),
                source = FoodSource.AI_RECOGNITION,
                confidence = recognized.confidence,
                imageUrl = imageUrl,
                isGbCertified = false
            )
        } catch (e: Exception) {
            null
        }
    }

    private fun createMockFoodFromBarcode(barcode: String): Food? {
        // 模拟条码查询结果
        return when {
            barcode.startsWith("69") -> Food(
                id = UUID.randomUUID().toString(),
                name = "某品牌牛奶",
                category = "乳制品",
                brand = "某品牌",
                barcode = barcode,
                nutrition = com.borealbit.gbdietitian.android.domain.model.NutritionInfo(
                    calories = 54.0,
                    protein = 3.0,
                    carbohydrates = 5.0,
                    fat = 3.2,
                    calcium = 104.0
                ),
                source = FoodSource.SCAN,
                isGbCertified = true
            )
            else -> null
        }
    }

    private fun getDefaultNutrition(): com.borealbit.gbdietitian.android.domain.model.NutritionInfo {
        return com.borealbit.gbdietitian.android.domain.model.NutritionInfo(
            calories = 0.0,
            protein = 0.0,
            carbohydrates = 0.0,
            fat = 0.0
        )
    }
}

/**
 * 识别结果
 */
sealed class RecognitionResult {
    object Loading : RecognitionResult()
    data class Success(val foods: List<Food>) : RecognitionResult()
    data class Error(val message: String) : RecognitionResult()
    data class NotFound(val message: String) : RecognitionResult()
    object NoFoodDetected : RecognitionResult()
}
