package com.borealbit.gbdietitian.android.domain.repository

import com.borealbit.gbdietitian.android.domain.model.UserProfile
import kotlinx.coroutines.flow.Flow

interface UserRepository {

    suspend fun getUserById(id: String): UserProfile?

    suspend fun getUserByEmail(email: String): UserProfile?

    suspend fun getCurrentUser(): UserProfile?

    fun getCurrentUserFlow(): Flow<UserProfile?>

    fun getAllUsers(): Flow<List<UserProfile>>

    suspend fun saveUser(user: UserProfile)

    suspend fun updateUser(user: UserProfile)

    suspend fun deleteUser(user: UserProfile)

    suspend fun deleteUserById(id: String)

    suspend fun getUserCount(): Int

    // 快速更新方法
    suspend fun updateWeight(userId: String, weight: Double)

    suspend fun updateTargetCalories(userId: String, calories: Double)

    suspend fun updateHealthConnectStatus(userId: String, enabled: Boolean)

    // 用户偏好
    suspend fun updateUserPreferences(userId: String, preferences: Map<String, Any>)
}
