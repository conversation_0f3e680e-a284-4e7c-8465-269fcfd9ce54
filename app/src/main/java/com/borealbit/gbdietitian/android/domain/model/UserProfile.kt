package com.borealbit.gbdietitian.android.domain.model

data class UserProfile(
    val id: String,
    val name: String,
    val email: String? = null,
    
    // 基本信息
    val age: Int,
    val gender: Gender,
    val height: Double, // 身高 (cm)
    val weight: Double, // 体重 (kg)
    val activityLevel: ActivityLevel,
    
    // 健康目标
    val healthGoal: HealthGoal,
    val targetWeight: Double? = null,
    val targetCalories: Double? = null,
    
    // 国标个性化设置
    val useGbStandards: Boolean = true,
    val customMacroRatios: MacroRatios? = null,
    
    // 健康状况
    val healthConditions: HealthConditions,
    val allergies: List<String> = emptyList(),
    val medications: List<String> = emptyList(),
    
    // 偏好设置
    val preferences: UserPreferences,
    
    // 元数据
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

enum class Gender {
    MALE, FEMALE, OTHER
}

enum class ActivityLevel {
    SEDENTARY,      // 久坐
    LIGHT,          // 轻度活动
    MODERATE,       // 中度活动
    ACTIVE,         // 活跃
    VERY_ACTIVE     // 非常活跃
}

enum class HealthGoal {
    MAINTAIN,       // 维持体重
    LOSE_WEIGHT,    // 减重
    GAIN_WEIGHT,    // 增重
    MUSCLE_GAIN     // 增肌
}

data class MacroRatios(
    val proteinRatio: Double, // 蛋白质比例 (0-1)
    val carbRatio: Double,    // 碳水比例 (0-1)
    val fatRatio: Double      // 脂肪比例 (0-1)
)

data class HealthConditions(
    val hasHypertension: Boolean = false,
    val hasDiabetes: Boolean = false,
    val hasHeartDisease: Boolean = false
)

data class UserPreferences(
    val preferredUnits: Units = Units.METRIC,
    val language: String = "zh",
    val enableNotifications: Boolean = true,
    val enableHealthConnect: Boolean = false
)

enum class Units {
    METRIC, IMPERIAL
}
