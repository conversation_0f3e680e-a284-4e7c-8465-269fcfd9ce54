package com.borealbit.gbdietitian.android.domain.model

data class Food(
    val id: String,
    val name: String,
    val nameEn: String? = null,
    val category: String,
    val brand: String? = null,
    val barcode: String? = null,
    
    // 营养成分 (每100g)
    val nutrition: NutritionInfo,
    
    // 国标相关
    val gbStandard: String? = null,
    val isGbCertified: Boolean = false,
    
    // 元数据
    val source: FoodSource,
    val confidence: Float? = null,
    val imageUrl: String? = null,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

data class NutritionInfo(
    val calories: Double, // 卡路里 (kcal)
    val protein: Double, // 蛋白质 (g)
    val carbohydrates: Double, // 碳水化合物 (g)
    val fat: Double, // 脂肪 (g)
    val fiber: Double? = null, // 膳食纤维 (g)
    val sugar: Double? = null, // 糖 (g)
    val sodium: Double? = null, // 钠 (mg)
    val potassium: Double? = null, // 钾 (mg)
    val calcium: Double? = null, // 钙 (mg)
    val iron: Double? = null, // 铁 (mg)
    val vitaminC: Double? = null, // 维生素C (mg)
    val vitaminA: Double? = null // 维生素A (μg)
)

enum class FoodSource {
    MANUAL,
    SCAN,
    API,
    AI_RECOGNITION
}

enum class FoodCategory {
    GRAINS,
    VEGETABLES,
    FRUITS,
    MEAT,
    SEAFOOD,
    DAIRY,
    NUTS,
    BEVERAGES,
    SNACKS,
    CONDIMENTS,
    OTHER
}
