package com.borealbit.gbdietitian.android.ai

import android.content.Context
import android.net.Uri
import com.borealbit.gbdietitian.android.domain.model.Food
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow

/**
 * AI模型管理器接口
 * 统一管理不同的AI模型（Gemini Nano、MLC-LLM、ONNX Runtime）
 */
interface AIModelManager {
    
    /**
     * 模型状态
     */
    val modelStatus: StateFlow<ModelStatus>
    
    /**
     * 初始化AI模型
     */
    suspend fun initialize(): Result<Unit>
    
    /**
     * 释放模型资源
     */
    suspend fun release()
    
    /**
     * 检查模型是否可用
     */
    fun isModelAvailable(): Boolean
    
    /**
     * 获取模型信息
     */
    fun getModelInfo(): ModelInfo
}

/**
 * 食物识别AI接口
 */
interface FoodRecognitionAI : AIModelManager {
    
    /**
     * 从图片识别食物
     */
    suspend fun recognizeFoodFromImage(imageUri: Uri): Result<List<RecognizedFood>>
    
    /**
     * 从描述文本识别食物
     */
    suspend fun recognizeFoodFromText(description: String): Result<List<RecognizedFood>>
}

/**
 * 营养问答AI接口
 */
interface NutritionChatAI : AIModelManager {
    
    /**
     * 回答营养相关问题
     */
    suspend fun answerNutritionQuestion(
        question: String,
        context: NutritionContext? = null
    ): Flow<ChatResponse>
    
    /**
     * 生成营养建议
     */
    suspend fun generateNutritionAdvice(
        userProfile: com.borealbit.gbdietitian.android.domain.model.UserProfile,
        nutritionData: com.borealbit.gbdietitian.android.domain.model.DailyNutritionSummary
    ): Result<String>
}

/**
 * 模型状态
 */
sealed class ModelStatus {
    object NotInitialized : ModelStatus()
    object Initializing : ModelStatus()
    object Ready : ModelStatus()
    object Error : ModelStatus()
    object Downloading : ModelStatus()
}

/**
 * 模型信息
 */
data class ModelInfo(
    val name: String,
    val version: String,
    val type: ModelType,
    val sizeInMB: Long,
    val supportedFeatures: List<AIFeature>
)

/**
 * 模型类型
 */
enum class ModelType {
    GEMINI_NANO,
    MLC_LLM,
    ONNX_RUNTIME,
    TENSORFLOW_LITE
}

/**
 * AI功能
 */
enum class AIFeature {
    FOOD_RECOGNITION,
    NUTRITION_CHAT,
    BARCODE_ANALYSIS,
    RECIPE_GENERATION
}

/**
 * 识别的食物
 */
data class RecognizedFood(
    val name: String,
    val confidence: Float,
    val category: String,
    val estimatedNutrition: com.borealbit.gbdietitian.android.domain.model.NutritionInfo?,
    val boundingBox: BoundingBox? = null
)

/**
 * 边界框
 */
data class BoundingBox(
    val left: Float,
    val top: Float,
    val right: Float,
    val bottom: Float
)

/**
 * 营养上下文
 */
data class NutritionContext(
    val userProfile: com.borealbit.gbdietitian.android.domain.model.UserProfile?,
    val recentLogs: List<com.borealbit.gbdietitian.android.domain.model.NutritionLog>,
    val currentGoals: Map<String, Double>
)

/**
 * 聊天响应
 */
data class ChatResponse(
    val content: String,
    val isComplete: Boolean,
    val confidence: Float? = null,
    val sources: List<String> = emptyList()
)
