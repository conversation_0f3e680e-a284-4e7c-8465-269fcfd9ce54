package com.borealbit.gbdietitian.android.data.local.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.PrimaryKey

@Entity(
    tableName = "nutrition_logs",
    foreignKeys = [
        ForeignKey(
            entity = FoodEntity::class,
            parentColumns = ["id"],
            childColumns = ["foodId"],
            onDelete = ForeignKey.CASCADE
        )
    ]
)
data class NutritionLogEntity(
    @PrimaryKey
    val id: String,
    val foodId: String,
    val userId: String? = null, // 用户ID，支持多用户
    
    // 摄入信息
    val amount: Double, // 摄入量 (g)
    val mealType: String, // 餐次: "breakfast", "lunch", "dinner", "snack"
    val consumedAt: Long, // 摄入时间戳
    
    // 计算后的营养值 (基于摄入量)
    val actualCalories: Double,
    val actualProtein: Double,
    val actualCarbohydrates: Double,
    val actualFat: Double,
    val actualFiber: Double? = null,
    val actualSugar: Double? = null,
    val actualSodium: Double? = null,
    
    // 记录方式
    val logMethod: String, // "photo", "barcode", "manual", "voice"
    val notes: String? = null, // 用户备注
    val imageUrl: String? = null, // 拍照记录的图片
    
    // 元数据
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)
