# GuoBiaoDietitian Android App

基于国标的智能营养师Android应用，采用Kotlin + Jetpack Compose + 离线大模型架构。

**包名**: `com.borealbit.gbdietitian.android`
**公司**: BorealBit

## 核心功能

- 🍎 **食物识别**: CameraX + AI视觉识别，拍照即可识别食物
- 📊 **国标解析**: 基于GB 28050-2011营养标签标准的智能解析
- 🤖 **离线AI**: 集成Gemini Nano/MLC-LLM，本地运行大模型
- 📈 **实时评估**: 营养素摄入量实时监控和健康建议
- 🔄 **健康同步**: 通过Health Connect与其他健康应用数据同步

## 技术栈

- **UI**: Jetpack Compose 2.x + Material 3
- **架构**: MVVM + Clean Architecture + Hilt DI
- **数据**: Room + DataStore + Kotlin Flow
- **AI**: Gemini Nano + MLC-LLM + ONNX Runtime
- **相机**: CameraX + ML Kit
- **健康**: Health Connect API

## 开发环境要求

- Android Studio Hedgehog | 2023.1.1+
- Kotlin 1.9.0+
- Gradle 8.0+
- 最低SDK: 24 (Android 7.0)
- 目标SDK: 34 (Android 14)