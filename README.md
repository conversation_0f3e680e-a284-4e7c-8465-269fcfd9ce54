# GuoBiaoDietitian Android App

基于国标的智能营养师Android应用，采用Kotlin + Jetpack Compose + 离线大模型架构。

**包名**: `com.borealbit.gbdietitian.android`
**公司**: BorealBit

## 🌟 核心功能

- 🍎 **食物识别**: CameraX + AI视觉识别，拍照即可识别食物
- 📊 **国标解析**: 基于GB 28050-2011营养标签标准的智能解析
- 🤖 **离线AI**: 集成Gemini Nano/MLC-LLM，本地运行大模型
- 📈 **实时评估**: 营养素摄入量实时监控和健康建议
- 🔄 **健康同步**: 通过Health Connect与其他健康应用数据同步

## 🛠 技术栈

- **UI**: Jetpack Compose 2.x + Material 3
- **架构**: MVVM + Clean Architecture + Hilt DI
- **数据**: Room + DataStore + Kotlin Flow
- **AI**: Gemini Nano + MLC-LLM + ONNX Runtime
- **相机**: CameraX + ML Kit
- **健康**: Health Connect API

## 📋 开发环境要求

- Android Studio Hedgehog | 2023.1.1+
- Kotlin 1.9.0+
- Gradle 8.0+
- 最低SDK: 24 (Android 7.0)
- 目标SDK: 34 (Android 14)
- Java 17+

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone https://github.com/dom-liu/gbdietitian-android-app.git
cd gbdietitian-android-app
```

### 2. 设置环境
确保已安装：
- Android Studio Hedgehog 或更新版本
- Android SDK 34
- Java 17+

### 3. 构建项目
```bash
# 使用构建脚本
chmod +x build.sh
./build.sh

# 或直接使用Gradle
./gradlew assembleDebug
```

### 4. 安装到设备
```bash
# 安装到连接的设备或模拟器
./gradlew installDebug
```

## 📁 项目结构

```
app/src/main/java/com/borealbit/gbdietitian/android/
├── ai/                     # AI模型集成
│   ├── gemini/            # Gemini Nano集成
│   ├── onnx/              # ONNX Runtime集成
│   └── AIModelManager.kt  # AI模型管理接口
├── data/                  # 数据层
│   ├── local/             # 本地数据（Room数据库）
│   ├── mapper/            # 数据映射器
│   └── repository/        # Repository实现
├── di/                    # 依赖注入模块
├── domain/                # 业务逻辑层
│   ├── model/             # 领域模型
│   ├── repository/        # Repository接口
│   └── usecase/           # 用例
└── presentation/          # UI层
    ├── components/        # 可复用UI组件
    ├── theme/             # Material 3主题
    └── MainActivity.kt    # 主Activity
```

## 🎯 核心特性详解

### 食物识别
- **拍照识别**: 使用CameraX + ONNX Runtime进行本地食物图像识别
- **条码扫描**: ML Kit条码扫描，支持GB 28050-2011营养标签
- **语音输入**: 语音转文本，快速记录食物摄入
- **手动输入**: 支持手动搜索和添加食物

### 国标解析引擎
- **GB 28050-2011**: 营养标签标准解析
- **膳食指南2022**: 基于最新中国居民膳食指南
- **个性化推荐**: 根据年龄、性别、活动量调整建议
- **实时预警**: 钠、糖、脂肪超标提醒

### AI模型支持
- **Gemini Nano**: 高端设备本地LLM，营养问答
- **ONNX Runtime**: 跨平台食物识别模型
- **MLC-LLM**: 备选本地大模型方案
- **离线优先**: 所有AI功能本地运行，保护隐私

## 🔧 开发指南

### 添加新的AI模型
1. 实现`AIModelManager`接口
2. 在`AIModule`中注册依赖
3. 更新模型选择逻辑

### 自定义UI组件
所有UI组件都基于Material 3设计，位于`presentation/components/`目录。

### 数据库迁移
Room数据库迁移文件位于`app/schemas/`目录。

## 🧪 测试

```bash
# 运行单元测试
./gradlew test

# 运行UI测试
./gradlew connectedAndroidTest
```

## 📱 支持的设备

### 最低要求
- Android 7.0 (API 24)+
- 2GB RAM
- 相机权限

### 推荐配置
- Android 12+ (支持动态颜色)
- 4GB+ RAM (AI模型运行)
- 支持Health Connect

### AI功能支持
- **Gemini Nano**: Pixel设备、Snapdragon 8 Gen3+
- **ONNX Runtime**: 所有Android设备
- **MLC-LLM**: 中高端设备 (4GB+ RAM)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [中国居民膳食指南2022](http://dg.cnsoc.org/)
- [GB 28050-2011 预包装食品营养标签通则](http://www.nhc.gov.cn/)
- [Jetpack Compose](https://developer.android.com/jetpack/compose)
- [Material Design 3](https://m3.material.io/)

## 📞 联系我们

- **开发者**: Dom Liu
- **邮箱**: <EMAIL>
- **公司**: BorealBit
- **项目主页**: https://github.com/dom-liu/gbdietitian-android-app

---

**国标营养师** - 让健康饮食更智能 🥗✨